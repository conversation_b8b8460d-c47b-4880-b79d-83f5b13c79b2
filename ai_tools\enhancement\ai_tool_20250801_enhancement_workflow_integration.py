#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 将智能提审服务集成到主工作流，确保Celery多进程环境下的线程安全
清理条件: 功能被替代时删除
"""

"""
工作流集成组件 - 智能提审服务集成
===============================

将第一阶段和第二阶段的成果集成到主工作流中：
1. 替换scheduler.py中的即时提审逻辑
2. 集成智能状态检查机制
3. 实现线程安全的批量提审
4. 确保Celery多进程环境兼容性

技术特性：
- 无缝集成现有工作流
- 保持向后兼容性
- 线程安全的数据库操作
- 智能错误恢复机制

创建时间: 2025-08-01
版本: v2.0 - 工作流集成版
"""

import time
from typing import Dict, Any, List, Tuple
from datetime import datetime, timezone
from collections import defaultdict
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import or_
from loguru import logger

from qianchuan_aw.database.models import Campaign, AdAccount
from qianchuan_aw.utils.db_utils import database_session
# 使用最新版本的生产环境优化提审服务
from qianchuan_aw.services.production_appeal_service import create_production_appeal_service


class SmartWorkflowIntegrator:
    """智能工作流集成器"""
    
    def __init__(self, app_settings: Dict[str, Any]):
        self.app_settings = app_settings
        # 使用最新版本的生产环境优化提审服务
        self.production_appeal_service = create_production_appeal_service(app_settings)
        
        # 集成配置
        self.config = {
            'max_batch_size': 10,       # 每批最多处理10个计划
            'batch_delay_seconds': 3,   # 批次间延迟3秒
            'enable_smart_timing': True, # 启用智能时机判断 (基于API状态，不是时间)
            'enable_status_check': True, # 启用状态检查
            'retry_interval_minutes': 5  # 首次提审失败后，5分钟后重试
        }
        
        logger.info("🔧 智能工作流集成器初始化完成")
    
    def handle_newly_created_plans_smart(self, db: Session) -> Dict[str, Any]:
        """智能处理新创建的计划 - 替换原有的即时提审逻辑"""
        
        logger.info("🎯 开始智能处理新创建的计划")
        logger.info("="*60)
        
        try:
            # 第一步：查找符合条件的计划
            candidate_plans = self._find_candidate_plans(db)
            
            if not candidate_plans:
                logger.info("✅ 没有找到需要提审的新计划")
                return {
                    'success': True,
                    'processed_count': 0,
                    'successful_count': 0,
                    'message': '没有需要处理的计划'
                }
            
            logger.info(f"📋 找到 {len(candidate_plans)} 个候选计划")
            
            # 第二步：额外的安全检查（防重复提审）
            safe_plans = self._additional_safety_check(candidate_plans)

            # 第三步：智能过滤（基于API状态和提审历史）
            if self.config['enable_smart_timing']:
                ready_plans = self._filter_plans_by_api_status_and_retry_timing(safe_plans)
                logger.info(f"🎯 状态和重试过滤后剩余 {len(ready_plans)} 个计划")
            else:
                ready_plans = safe_plans

            if not ready_plans:
                logger.info("⏰ 所有计划都暂时不符合提审条件")
                return {
                    'success': True,
                    'processed_count': 0,
                    'successful_count': 0,
                    'message': '所有计划都暂时不符合提审条件'
                }
            
            # 第四步：按账户分组批量处理
            grouped_plans = self._group_plans_by_account(ready_plans)
            logger.info(f"👥 按账户分组: {len(grouped_plans)} 个账户")

            # 第五步：执行智能批量提审
            results = self._execute_smart_batch_appeal(db, grouped_plans)

            # 第六步：统计和返回结果
            return self._summarize_results(results)
            
        except Exception as e:
            logger.error(f"❌ 智能处理新计划失败: {e}", exc_info=True)
            return {
                'success': False,
                'processed_count': 0,
                'successful_count': 0,
                'error': str(e)
            }
    
    def _find_candidate_plans(self, db: Session) -> List[Campaign]:
        """查找候选计划 - 使用与原scheduler相同的逻辑"""
        
        plans = db.query(Campaign).options(
            joinedload(Campaign.account).joinedload(AdAccount.principal)
        ).filter(
            Campaign.status == 'AUDITING',
            # 确保未提审过：appeal_status为空且first_appeal_at为空
            Campaign.appeal_status.is_(None),
            Campaign.first_appeal_at.is_(None),
            # 额外保险：appeal_attempt_count为空或为0
            or_(Campaign.appeal_attempt_count.is_(None), Campaign.appeal_attempt_count == 0)
        ).all()
        
        logger.info(f"🔍 数据库查询找到 {len(plans)} 个AUDITING状态的未提审计划")
        
        return plans

    def _additional_safety_check(self, plans: List[Campaign]) -> List[Campaign]:
        """额外的安全检查 - 防止重复提审"""

        safe_plans = []

        for plan in plans:
            # 使用增强的防重复逻辑
            should_skip, reason = self._should_skip_plan_for_appeal(plan)

            if should_skip:
                logger.warning(f"⚠️ 跳过计划 {plan.campaign_id_qc}: {reason}")
                continue

            safe_plans.append(plan)

        logger.info(f"🛡️ 安全检查后确认 {len(safe_plans)}/{len(plans)} 个计划可以提审")
        return safe_plans

    def _should_skip_plan_for_appeal(self, plan: Campaign) -> Tuple[bool, str]:
        """检查计划是否应该跳过提审 - 防重复核心逻辑"""

        # 检查1：已经成功提审的计划
        if plan.appeal_status == 'appeal_pending':
            return True, f"计划已成功提审，状态为appeal_pending，跳过重复提审"

        # 检查2：已经有提审历史的计划
        if plan.first_appeal_at is not None:
            return True, f"计划已有提审历史 (首次提审: {plan.first_appeal_at})，跳过重复提审"

        # 检查3：尝试次数过多的计划
        if plan.appeal_attempt_count and plan.appeal_attempt_count >= 3:
            return True, f"计划提审尝试次数过多 ({plan.appeal_attempt_count}次)，跳过继续尝试"

        # 检查4：状态不符合的计划
        if plan.status != 'AUDITING':
            return True, f"计划状态不是AUDITING ({plan.status})，不需要提审"

        return False, "计划符合提审条件"

    def _filter_plans_by_api_status_and_retry_timing(self, plans: List[Campaign]) -> List[Campaign]:
        """基于API状态和重试时机过滤计划"""

        ready_plans = []
        now = datetime.now(timezone.utc)
        retry_interval_seconds = self.config['retry_interval_minutes'] * 60

        for plan in plans:
            # 1. 检查是否已经成功提审过
            if plan.appeal_status == 'appeal_pending':
                logger.debug(f"⏭️ 计划 {plan.campaign_id_qc} 已成功提审，跳过")
                continue

            # 2. 检查API状态是否符合提审条件
            from ai_tool_20250801_enhancement_plan_status_checker import PlanStatusChecker
            status_checker = PlanStatusChecker(self.app_settings)

            # 使用数据库会话检查状态
            with database_session() as db:
                ready, reason, api_status = status_checker.check_plan_ready_for_appeal(db, plan)

                if not ready:
                    logger.debug(f"❌ 计划 {plan.campaign_id_qc} API状态不符合: {reason}")
                    continue

            # 3. 检查重试时机（如果之前提审失败过）
            if plan.appeal_attempt_count and plan.appeal_attempt_count > 0:
                # 有提审失败历史，检查重试间隔
                if plan.last_appeal_at:
                    time_since_last_attempt = (now - plan.last_appeal_at).total_seconds()

                    if time_since_last_attempt < retry_interval_seconds:
                        wait_minutes = (retry_interval_seconds - time_since_last_attempt) / 60
                        logger.debug(f"⏰ 计划 {plan.campaign_id_qc} 需要再等待 {wait_minutes:.1f}分钟后重试")
                        continue
                    else:
                        logger.debug(f"🔄 计划 {plan.campaign_id_qc} 可以重试提审")
                else:
                    # 有提审次数但没有时间记录，保守处理
                    logger.debug(f"⚠️ 计划 {plan.campaign_id_qc} 提审历史异常，保守处理")
            else:
                # 首次提审，直接符合条件
                logger.debug(f"🆕 计划 {plan.campaign_id_qc} 首次提审，符合条件")

            ready_plans.append(plan)

        return ready_plans
    
    def _group_plans_by_account(self, plans: List[Campaign]) -> Dict[str, List[Campaign]]:
        """按账户分组计划"""
        
        grouped = defaultdict(list)
        
        for plan in plans:
            account_key = f"{plan.account.principal.name}_{plan.account.account_id_qc}"
            grouped[account_key].append(plan)
        
        # 限制每组的大小
        limited_grouped = {}
        for account_key, account_plans in grouped.items():
            if len(account_plans) > self.config['max_batch_size']:
                logger.warning(f"⚠️ 账户 {account_key} 有 {len(account_plans)} 个计划，限制为 {self.config['max_batch_size']} 个")
                limited_grouped[account_key] = account_plans[:self.config['max_batch_size']]
            else:
                limited_grouped[account_key] = account_plans
        
        return limited_grouped
    
    def _execute_smart_batch_appeal(self, db: Session, grouped_plans: Dict[str, List[Campaign]]) -> List[Dict[str, Any]]:
        """执行智能批量提审"""
        
        all_results = []
        
        for i, (account_key, account_plans) in enumerate(grouped_plans.items(), 1):
            logger.info(f"\n📋 处理第 {i}/{len(grouped_plans)} 个账户: {account_key}")
            logger.info(f"📊 该账户有 {len(account_plans)} 个计划")
            logger.info("-" * 50)
            
            account_results = []
            
            # 逐个处理该账户的计划
            for j, plan in enumerate(account_plans, 1):
                logger.info(f"🎯 处理第 {j}/{len(account_plans)} 个计划: {plan.campaign_id_qc}")
                
                try:
                    # 准备计划数据
                    plan_data = {
                        'campaign_id': plan.campaign_id_qc,
                        'principal_name': plan.account.principal.name if plan.account.principal else "未知主体",
                        'account_id': plan.account.account_id_qc
                    }

                    logger.info(f"🚀 使用生产环境优化提审服务: 主体={plan_data['principal_name']}, 账户={plan_data['account_id']}, 计划={plan_data['campaign_id']}")

                    # 使用生产环境优化的批量提审服务（单个计划）
                    results = self.production_appeal_service.batch_appeal_for_account(
                        principal_name=plan_data['principal_name'],
                        account_id=plan_data['account_id'],
                        plans=[plan_data]
                    )

                    # 获取结果
                    if results and len(results) > 0:
                        result = results[0]
                        success = result['success']
                        message = result['message']
                        details = result
                    else:
                        success = False
                        message = "未收到提审结果"
                        details = {}
                    
                    result = {
                        'campaign_id': plan.campaign_id_qc,
                        'account_key': account_key,
                        'success': success,
                        'message': message,
                        'details': details,
                        'processed_at': datetime.now(timezone.utc)
                    }
                    
                    account_results.append(result)

                    # 立即更新数据库状态 - 关键修复！
                    self._update_campaign_appeal_status(db, plan, result)

                    if success:
                        logger.success(f"✅ 计划 {plan.campaign_id_qc} 智能提审成功")
                    else:
                        logger.warning(f"⚠️ 计划 {plan.campaign_id_qc} 智能提审失败: {message}")

                    # 计划间延迟
                    if j < len(account_plans):
                        logger.info("⏳ 等待2秒后处理下一个计划...")
                        time.sleep(2)
                        
                except Exception as e:
                    logger.error(f"❌ 计划 {plan.campaign_id_qc} 处理异常: {e}")
                    
                    result = {
                        'campaign_id': plan.campaign_id_qc,
                        'account_key': account_key,
                        'success': False,
                        'message': f"处理异常: {str(e)}",
                        'details': {},
                        'processed_at': datetime.now(timezone.utc)
                    }
                    
                    account_results.append(result)
            
            all_results.extend(account_results)
            
            # 提交该账户的数据库更改
            try:
                db.commit()
                logger.success(f"✅ 账户 {account_key} 的数据库更改已提交")
            except Exception as e:
                logger.error(f"❌ 账户 {account_key} 数据库提交失败: {e}")
                db.rollback()
            
            # 账户间延迟
            if i < len(grouped_plans):
                logger.info(f"⏳ 等待 {self.config['batch_delay_seconds']} 秒后处理下一个账户...")
                time.sleep(self.config['batch_delay_seconds'])
        
        return all_results

    def _update_campaign_appeal_status(self, db: Session, campaign: Campaign, result: Dict[str, Any]):
        """更新计划的提审状态到数据库 - 关键修复方法"""

        try:
            if result['success']:
                # 提审成功，更新状态
                old_status = campaign.appeal_status
                campaign.appeal_status = 'appeal_pending'

                # 记录首次提审时间
                if not campaign.first_appeal_at:
                    campaign.first_appeal_at = datetime.now(timezone.utc)

                # 更新最后提审时间
                campaign.last_appeal_at = datetime.now(timezone.utc)

                # 更新申诉尝试次数（重要：防止无限提审）
                campaign.appeal_attempt_count = (campaign.appeal_attempt_count or 0) + 1

                # 清除错误信息
                campaign.appeal_error_message = None

                # 更新最后修改时间
                campaign.updated_at = datetime.now(timezone.utc)

                logger.success(f"💾 计划 {campaign.campaign_id_qc} 数据库状态: {old_status} → appeal_pending (第{campaign.appeal_attempt_count}次提审)")

            else:
                # 提审失败，记录错误信息
                campaign.appeal_status = 'submission_failed'
                campaign.appeal_error_message = result['message'][:500] if result['message'] else '提审失败'
                campaign.last_appeal_at = datetime.now(timezone.utc)
                campaign.appeal_attempt_count = (campaign.appeal_attempt_count or 0) + 1
                campaign.updated_at = datetime.now(timezone.utc)

                logger.warning(f"💾 计划 {campaign.campaign_id_qc} 记录失败状态: {result['message'][:100]}...")

            # 立即刷新到数据库
            db.flush()

        except Exception as e:
            logger.error(f"❌ 更新计划 {campaign.campaign_id_qc} 数据库状态失败: {e}")
            db.rollback()
            raise

    def _summarize_results(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """汇总结果"""
        
        total_count = len(results)
        successful_count = sum(1 for r in results if r['success'])
        failed_count = total_count - successful_count
        
        success_rate = (successful_count / total_count * 100) if total_count > 0 else 0
        
        # 性能统计 (使用现有申诉服务，无需单独统计)
        performance_stats = {
            'total_appeals': successful_count,
            'success_rate': success_rate,
            'average_duration': 'N/A'  # 防弹级申诉服务内部处理
        }
        
        summary = {
            'success': True,
            'processed_count': total_count,
            'successful_count': successful_count,
            'failed_count': failed_count,
            'success_rate': success_rate,
            'performance_stats': performance_stats,
            'message': f'智能提审完成: {successful_count}/{total_count} 成功 ({success_rate:.1f}%)'
        }
        
        logger.info("📊 智能提审结果汇总:")
        logger.info(f"   总计划数: {total_count}")
        logger.info(f"   成功数量: {successful_count}")
        logger.info(f"   失败数量: {failed_count}")
        logger.info(f"   成功率: {success_rate:.1f}%")
        
        return summary


# 工作流集成函数 - 用于替换scheduler.py中的原有函数
def handle_plan_submission_smart(db: Session, app_settings: Dict[str, Any]) -> Dict[str, Any]:
    """智能计划提审处理 - 替换原有的handle_plan_submission函数"""
    
    logger.info("🚀 启动智能计划提审处理 [V2.1 - 精准时机版]")
    logger.info("="*80)
    logger.info("🎯 V2.1 优化特性:")
    logger.info("1. API状态精准检查 - 只有符合提审条件的计划才处理")
    logger.info("2. 智能重试机制 - 首次失败后5分钟重试，不再模糊等待")
    logger.info("3. 线程安全浏览器 - 解决多进程环境问题")
    logger.info("4. 增强输入框定位 - 解决聊天输入框定位失败")
    logger.info("5. 提审历史检查 - 避免重复提审已成功的计划")
    logger.info("="*80)
    
    try:
        integrator = SmartWorkflowIntegrator(app_settings)
        result = integrator.handle_newly_created_plans_smart(db)
        
        if result['success']:
            logger.success(f"✅ {result['message']}")
        else:
            logger.error(f"❌ 智能提审失败: {result.get('error', '未知错误')}")
        
        return result
        
    except Exception as e:
        logger.error(f"❌ 智能计划提审处理异常: {e}", exc_info=True)
        return {
            'success': False,
            'processed_count': 0,
            'successful_count': 0,
            'error': str(e)
        }


# 测试函数
def test_smart_workflow_integration():
    """测试智能工作流集成"""
    
    logger.info("🧪 开始测试智能工作流集成")
    
    try:
        from qianchuan_aw.utils.config_manager import get_config_manager
        
        config_manager = get_config_manager()
        app_settings = config_manager.get_config()
        
        with database_session() as db:
            result = handle_plan_submission_smart(db, app_settings)
            
            logger.info(f"🎯 测试结果: {result}")
            
            if result['success']:
                logger.success("✅ 智能工作流集成测试成功")
            else:
                logger.warning("⚠️ 智能工作流集成测试失败")
                
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}", exc_info=True)


if __name__ == "__main__":
    test_smart_workflow_integration()
