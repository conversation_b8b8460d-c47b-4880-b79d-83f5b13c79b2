import requests
import time
import json
import os
import hashlib
from typing import List, Dict, Any, Optional

# [V42.0 灯塔计划] 切换到统一的数据库会话和模型
from qianchuan_aw.database.database import SessionLocal
from qianchuan_aw.database.models import AuthCredential, Principal
from qianchuan_aw.utils.logger import logger
from qianchuan_aw.utils.rate_limiter import create_rate_limiter

# [V35.0] 核心监控指标列表，严格与用户指定及API文档对齐
FAST_MONITOR_MATERIAL_REPORT_FIELDS = [
    "stat_cost", "prepay_and_pay_order_roi", "pay_order_count", "pay_order_amount",
    "ctr", "ecp_convert_rate", "cpm_platform", "ecp_cpa_platform", "ecp_convert_cnt",
    "show_cnt", "click_cnt", "dy_comment", "dy_share",
    "qianchuan_effective_view_convert_count", "live_watch_one_minute_count",
    "luban_live_enter_cnt"
]

AD_BASE_URL = "https://ad.oceanengine.com/open_api/"
API_BASE_URL = "https://api.oceanengine.com/open_api/"

class QianchuanAPIException(Exception):
    def __init__(self, code, message, request_id):
        self.code = code
        self.message = message
        self.request_id = request_id
        super().__init__(f"API Error - Code: {code}, Message: '{message}', Request ID: {request_id}")


class AccountBlockedException(QianchuanAPIException):
    """当账户因撞审或被平台限制创建计划时抛出的特定异常。"""
    pass

class QianchuanClient:
    def __init__(self, app_id: str, secret: str, principal_id: int):
        self.app_id = app_id
        self.secret = secret
        self.principal_id = principal_id

        self.access_token = None
        self.refresh_token = None
        self.token_expires_at = 0
        self.refresh_token_expires_at = 0

        # 初始化频控管理器
        self._init_rate_limiter()

        self._load_token_from_db()

        if not self.is_token_valid():
            logger.warning(f"主体 {self.principal_id} 的初始Token无效或已过期，将立即尝试刷新。")
            self.refresh_access_token()

    def _init_rate_limiter(self):
        """初始化API频控管理器"""
        try:
            from qianchuan_aw.utils.config_manager import get_config_manager
            config_manager = get_config_manager()
            rate_limit_config = config_manager.get('rate_limiting', {})

            self.rate_limiter = create_rate_limiter(rate_limit_config)

            if self.rate_limiter.enabled:
                logger.info(f"主体 {self.principal_id} 已启用API频控")
            else:
                logger.debug(f"主体 {self.principal_id} 未启用API频控")

        except Exception as e:
            logger.warning(f"初始化频控管理器失败: {e}，将禁用频控功能")
            # 创建一个禁用的频控管理器作为fallback
            self.rate_limiter = create_rate_limiter({"enabled": False})

    def _request(self, method: str, path: str, base_url_override: str = None, params: dict = None, json_data: dict = None, data: dict = None, files: dict = None, headers: dict = None, is_auth_call: bool = False, timeout: int = 10):
        from qianchuan_aw.utils.retry import api_retry
        from qianchuan_aw.utils.exceptions import QianchuanAPIError, QianchuanNetworkError

        @api_retry(max_attempts=3)
        def _make_api_call():
            nonlocal headers  # 允许修改外部的 headers 变量

            # 频控检查 - 在实际请求前获取令牌
            if not is_auth_call:  # 认证调用不受频控限制
                endpoint = self._get_endpoint_name(path)
                if not self.rate_limiter.acquire(endpoint=endpoint, timeout=5.0):
                    logger.warning(f"API频控限制，无法获取请求令牌: {path}")
                    raise QianchuanAPIError("Rate limit exceeded, please retry later", error_code="40100")

            if not is_auth_call:
                if not self.is_token_valid():
                    logger.warning(f"主体 {self.principal_id} 的 Access token 无效或已过期，正在尝试刷新...")
                    if not self.refresh_access_token():
                        raise QianchuanAPIError("Token refresh failed. Please re-authenticate.", error_code="-1")

                if headers is None:
                    headers = {}
                if "Access-Token" not in headers:
                    if self.access_token:
                        headers["Access-Token"] = self.access_token
                    else:
                        logger.error("尝试进行非认证API调用，但 Access Token 为空。")
                        raise QianchuanAPIError("Access token is missing or invalid for this request.", error_code="-1")

            final_base_url = base_url_override or AD_BASE_URL
            url = f"{final_base_url}{path}"

            if headers is None:
                headers = {}

            logger.debug(f"最终请求URL: {url}, 请求头: {headers}")

            try:
                response = requests.request(method, url, params=params, json=json_data, data=data, files=files, headers=headers, timeout=timeout)
                response.raise_for_status()
            except requests.exceptions.RequestException as e:
                raise QianchuanNetworkError(f"网络请求失败: {str(e)}", details={"url": url, "method": method})

            try:
                response_json = response.json()
                logger.debug(f"收到千川 API 原始响应: {json.dumps(response_json, ensure_ascii=False)}")
            except json.JSONDecodeError as e:
                logger.error(f"API响应内容不是有效的JSON格式。Status: {response.status_code}, Response Text: '{response.text}'")
                raise QianchuanAPIError(f"Invalid JSON response: {response.text}", error_code="-2")

            if response_json.get("code") != 0:
                api_code = response_json.get("code")
                api_message = response_json.get("message", "Unknown API error")
                request_id = response_json.get("request_id")

                # 检查是否为Token失效错误
                if api_code == 40102 and not is_auth_call:
                    logger.warning("API返回Token失效，强制刷新一次...")
                    if self.refresh_access_token():
                        headers["Access-Token"] = self.access_token
                        logger.info("Token刷新成功，将立即重试请求...")
                        # 递归调用自己进行重试
                        return self._request(method, path, base_url_override, params, json_data, data, files, headers, is_auth_call, timeout)

                # 检查是否为频控错误
                if api_code == 40100 and not is_auth_call:
                    endpoint = self._get_endpoint_name(path)
                    logger.warning(f"API返回频控错误40100: {api_message}")
                    self.rate_limiter.handle_rate_limit_error("40100", endpoint)
                    # 频控错误会被重试机制处理，这里只记录和调整速率

                # 对于API错误，抛出自定义异常
                logger.error(f"API请求失败。URL: {url}, Method: {method}, Code: {api_code}, Message: {api_message}")

                # 详细记录完整的响应信息用于调试
                logger.error(f"🔍 完整API响应信息:")
                logger.error(f"  HTTP状态码: {response.status_code}")
                logger.error(f"  响应头: {dict(response.headers)}")
                logger.error(f"  完整响应体: {response_json}")

                # 特别针对40000错误记录更多信息
                if api_code == 40000:
                    logger.error(f"🎯 40000错误详细分析:")
                    logger.error(f"  请求URL: {url}")
                    logger.error(f"  请求方法: {method}")
                    logger.error(f"  请求头: {headers if 'headers' in locals() else '未知'}")
                    logger.error(f"  请求体: {json_data if 'json_data' in locals() else '未知'}")
                    logger.error(f"  错误信息: {api_message}")
                    logger.error(f"  可能原因: 1)账户未在PC端完成首次授权 2)Token权限不足 3)参数错误 4)账户状态异常")

                raise QianchuanAPIError(
                    message=api_message,
                    error_code=str(api_code),
                    response_data=response_json,
                    status_code=response.status_code
                )

            return response_json

        # 调用内部函数
        return _make_api_call()

    def _get_endpoint_name(self, path: str) -> str:
        """
        从API路径提取端点名称，用于频控管理

        Args:
            path: API路径

        Returns:
            str: 端点名称
        """
        # 简单的端点名称映射
        if "campaign/create" in path:
            return "create_campaign"
        elif "file/video" in path:
            return "upload_video"
        elif "creative/create" in path:
            return "create_creative"
        elif "ad/create" in path:
            return "create_ad"
        else:
            # 默认返回路径的第一部分
            parts = path.strip('/').split('/')
            return parts[0] if parts else "unknown"

    def upload_video(self, advertiser_id: int, video_file_path: str, video_signature: str, is_aigc: bool = False):
        """[V-Final-Fix-3] 上传视频，修复文件句柄问题"""
        if not os.path.exists(video_file_path):
            logger.error(f"视频文件不存在: {video_file_path}")
            return None

        logger.info(f"准备上传视频: {video_file_path}...")

        path = "2/file/video/ad/"

        form_data = {
            'advertiser_id': str(advertiser_id),
            'upload_type': 'UPLOAD_BY_FILE',
            'video_signature': video_signature,
            'is_aigc': str(is_aigc).lower()
        }

        try:
            # 修复：先读取文件内容，然后传递给API
            with open(video_file_path, 'rb') as f:
                file_content = f.read()

            # 使用文件内容创建files字典
            files = {'video_file': (os.path.basename(video_file_path), file_content, 'video/mp4')}
            # 调用API，使用较短的超时时间
            response_data = self._request("POST", path, data=form_data, files=files, timeout=60)

            data = response_data.get("data", {})
            logger.success(f"视频上传成功! Response data: {json.dumps(data, ensure_ascii=False)}")
            return data
        except (QianchuanAPIException, requests.exceptions.RequestException) as e:
            logger.error(f"视频上传失败: {e}")
            return None

    def _load_token_from_db(self):
        from qianchuan_aw.utils.db_utils import database_session
        from qianchuan_aw.utils.exceptions import QianchuanDatabaseError

        try:
            with database_session() as db:
                credential = db.query(AuthCredential).filter(AuthCredential.principal_id == self.principal_id).first()
                if credential:
                    logger.info(f"为广告主主体 {self.principal_id} 从统一数据库加载了Token。")
                    self.access_token = credential.access_token
                    self.refresh_token = credential.refresh_token
                    self.token_expires_at = credential.token_expires_at
                    self.refresh_token_expires_at = credential.refresh_token_expires_at
                    # 打印 access_token 的部分值和长度，以便调试
                    token_display = self.access_token[:8] + '...' + self.access_token[-4:] if self.access_token and len(self.access_token) > 12 else self.access_token
                    logger.debug(f"加载后 Access Token: {token_display} (长度: {len(self.access_token) if self.access_token else 0}), 过期时间: {self.token_expires_at}")
        except Exception as e:
            raise QianchuanDatabaseError(f"从数据库加载Token失败: {str(e)}", details={"principal_id": self.principal_id})

    def _update_tokens(self, token_data: dict):
        """
        V7.3 健壮的Token更新与持久化逻辑。
        - access_token 总是更新。
        - refresh_token 仅在API响应中存在时才更新，防止被刷新操作的空值覆盖。
        """
        # 1. 更新内存中的 access_token
        self.access_token = token_data.get("access_token")
        expires_in = token_data.get("expires_in", 0)
        self.token_expires_at = int(time.time() + expires_in - 60) # 提前60秒过期
        # 打印 access_token 的部分值和长度，以便调试
        token_display = self.access_token[:8] + '...' + self.access_token[-4:] if self.access_token and len(self.access_token) > 12 else self.access_token
        logger.debug(f"更新后 Access Token: {token_display} (长度: {len(self.access_token) if self.access_token else 0}), 过期时间: {self.token_expires_at}")

        # 2. 条件更新内存中的 refresh_token
        if "refresh_token" in token_data:
            self.refresh_token = token_data.get("refresh_token")
            refresh_expires_in = token_data.get("refresh_token_expires_in", 0)
            self.refresh_token_expires_at = int(time.time() + refresh_expires_in - 60)

        # 3. 将内存中的最新状态持久化到数据库
        from qianchuan_aw.utils.db_utils import database_session
        from qianchuan_aw.utils.exceptions import QianchuanDatabaseError

        try:
            with database_session() as db:
                credential = db.query(AuthCredential).filter(AuthCredential.principal_id == self.principal_id).first()
                if not credential:
                    logger.info(f"正在为广告主主体 {self.principal_id} 在统一数据库中创建新Token记录...")
                    credential = AuthCredential(principal_id=self.principal_id)
                    db.add(credential)

                logger.info(f"正在更新主体 {self.principal_id} 的数据库Token...")
                credential.access_token = self.access_token
                credential.token_expires_at = self.token_expires_at

                # 同样，只有在内存中有新的 refresh_token 时才更新数据库
                if "refresh_token" in token_data:
                    credential.refresh_token = self.refresh_token
                    credential.refresh_token_expires_at = self.refresh_token_expires_at
        except Exception as e:
            raise QianchuanDatabaseError(f"更新Token到数据库失败: {str(e)}", details={"principal_id": self.principal_id})

    def _clear_tokens(self):
        self.access_token = None
        self.refresh_token = None
        self.token_expires_at = 0
        self.refresh_token_expires_at = 0

        from qianchuan_aw.utils.db_utils import database_session
        from qianchuan_aw.utils.exceptions import QianchuanDatabaseError

        try:
            with database_session() as db:
                credential = db.query(AuthCredential).filter(AuthCredential.principal_id == self.principal_id).first()
                if credential:
                    logger.info(f"正在从统一数据库删除主体 {self.principal_id} 的Token记录...")
                    db.delete(credential)
        except Exception as e:
            logger.warning(f"清理Token记录时出错: {str(e)}", extra={"principal_id": self.principal_id})

    def get_access_token(self, auth_code: str) -> Dict[str, Any] | None:
        """[V57.3 修复] 使用 auth_code 获取 access_token。"""
        logger.info(f"正在使用 auth_code '{auth_code}' 获取 access_token...")
        path = "oauth2/access_token/"
        payload = {"app_id": self.app_id, "secret": self.secret, "grant_type": "auth_code", "auth_code": auth_code}
        try:
            # 此接口在 AD_BASE_URL 下
            response_data = self._request("POST", path, base_url_override=AD_BASE_URL, json_data=payload, is_auth_call=True)
            data = response_data.get("data", {})
            if not data.get("access_token"):
                raise QianchuanAPIException(code=-1, message=f"API响应中未找到access_token: {response_data}", request_id=response_data.get("request_id"))
            
            self._update_tokens(data)
            logger.success("成功获取并存储 access_token！")
            return data
        except (QianchuanAPIException, requests.exceptions.RequestException) as e:
            logger.error(f"获取 access_token 失败: {e}")
            self._clear_tokens()
            return None

    def is_token_valid(self) -> bool:
        is_valid = self.access_token and time.time() < self.token_expires_at
        logger.debug(f"检查Token有效性: Access Token {'存在' if self.access_token else '不存在'}, 当前时间: {int(time.time())}, 过期时间: {self.token_expires_at}, 结果: {is_valid}")
        return is_valid

    def refresh_access_token(self) -> bool:
        logger.info(f"尝试刷新 Access Token。当前 Refresh Token: {'<存在>' if self.refresh_token else '<空>'}")
        if not self.refresh_token:
            logger.error("没有可用的 refresh_token 供刷新。")
            return False
        
        current_time = int(time.time())
        if current_time >= self.refresh_token_expires_at:
            logger.error(f"Refresh token 已过期 (过期时间: {self.refresh_token_expires_at}, 当前时间: {current_time})，需要重新手动授权。")
            self._clear_tokens()
            return False

        logger.info("正在刷新 access_token...")
        path = "oauth2/refresh_token/"
        payload = {"app_id": self.app_id, "secret": self.secret, "grant_type": "refresh_token", "refresh_token": self.refresh_token}
        try:
            response_data = self._request("POST", path, base_url_override=AD_BASE_URL, json_data=payload, is_auth_call=True)
            data = response_data.get("data", {})
            self._update_tokens(data)
            logger.success("成功刷新并存储了新的 access_token！")
            logger.debug(f"刷新后 Access Token: {'<存在>' if self.access_token else '<空>'}, 过期时间: {self.token_expires_at}")
            return True
        except (QianchuanAPIException, requests.exceptions.RequestException) as e:
            logger.error(f"刷新 access_token 失败: {e}")
            self._clear_tokens()
            logger.debug("刷新失败后已清除Token。")
            return False

    def get_library_videos(self, advertiser_id: int, filtering: dict, page: int = 1, page_size: int = 20):
        logger.debug(f"正在为广告主 {advertiser_id} 从素材库获取视频...")
        path = "v1.0/qianchuan/video/get/"
        params = {"advertiser_id": advertiser_id, "filtering": json.dumps(filtering), "page": page, "page_size": page_size}
        try:
            response_data = self._request("GET", path, params=params)
            data = response_data.get("data", {})
            # 返回整个 data 对象，以便调用方可以访问分页信息
            if "list" in data:
                logger.info(f"成功获取到 {len(data['list'])} 条视频素材。")
            return data
        except (QianchuanAPIException, requests.exceptions.RequestException) as e:
            logger.error(f"从素材库获取视频失败: {e}")
            return None

    def get_campaign_group_list(self, advertiser_id: int, page: int = 1, page_size: int = 10) -> List[Dict[str, Any]]:
        """[V-Final] 获取广告组列表"""
        logger.info(f"正在为广告主 {advertiser_id} 获取广告组列表...")
        path = "v1.0/qianchuan/campaign_list/get/"
        
        filter_params = {"marketing_goal": "LIVE_PROM_GOODS"}
        request_params = {
            "advertiser_id": advertiser_id,
            "page": page,
            "page_size": page_size,
            "filter": json.dumps(filter_params)
        }
        
        try:
            response_data = self._request("GET", path, params=request_params)
            data = response_data.get("data", {})
            campaigns = data.get("list", [])
            logger.info(f"成功获取到 {len(campaigns)} 个广告组。")
            return campaigns
        except (QianchuanAPIException, requests.exceptions.RequestException) as e:
            logger.error(f"获取广告组列表失败: {e}")
            return []

    def get_ad_plan_list(self, advertiser_id: int, filtering: Dict[str, Any]) -> List[Dict[str, Any]]:
        """[V38.0] 获取广告计划列表详情，支持灵活过滤和自动分页"""
        logger.info(f"正在为广告主 {advertiser_id} 获取符合条件的计划列表...")
        path = "v1.0/qianchuan/ad/get/"

        final_filter = filtering.copy()
        final_filter.setdefault("marketing_goal", "LIVE_PROM_GOODS")

        # 确保aweme_id是整数类型
        if "aweme_id" in final_filter and final_filter["aweme_id"] is not None:
            try:
                final_filter["aweme_id"] = int(final_filter["aweme_id"])
                logger.info(f"aweme_id已转换为整数: {final_filter['aweme_id']}")
            except (ValueError, TypeError) as e:
                logger.error(f"aweme_id转换失败: {final_filter['aweme_id']}, 错误: {e}")
                # 移除无效的aweme_id参数
                del final_filter["aweme_id"]

        # 调试：打印最终的filtering参数
        logger.info(f"最终filtering参数: {final_filter}")

        all_plans = []
        page = 1
        page_size = 100  # 使用较大的页面大小以提高效率

        while True:
            request_params = {
                "advertiser_id": advertiser_id,
                "filtering": json.dumps(final_filter),
                "page": page,
                "page_size": page_size,
                "request_aweme_info": 1 # [V38.0] 总是请求抖音号信息
            }

            try:
                response_data = self._request("GET", path, params=request_params)
                data = response_data.get("data", {})
                plans = data.get("list", [])
                
                if not plans:
                    break  # 没有更多计划，退出循环

                all_plans.extend(plans)

                page_info = data.get("page_info", {})
                if page >= page_info.get("total_page", 1):
                    break  # 到达最后一页

                page += 1

            except (QianchuanAPIException, requests.exceptions.RequestException) as e:
                logger.error(f"获取广告计划列表失败 (页码: {page}): {e}")
                # 即使出错也返回已获取的部分
                return all_plans if all_plans else []

        logger.info(f"成功获取到 {len(all_plans)} 个广告计划。")
        return all_plans

    def get_ad_plan_detail(self, advertiser_id: int, ad_id: int) -> Dict[str, Any] | None:
        """[V38.0] 获取单个广告计划的完整详情，包含创意信息"""
        logger.info(f"正在为广告主 {advertiser_id} 获取计划 {ad_id} 的完整详情...")
        path = "v1.0/qianchuan/ad/detail/get/"
        
        params = {
            "advertiser_id": advertiser_id,
            "ad_id": ad_id,
            "request_material_url": True # 总是请求素材URL，用于复制
        }
        
        try:
            response_data = self._request("GET", path, params=params)
            data = response_data.get("data", {})
            logger.success(f"成功获取到计划 {ad_id} 的详情。")
            return data
        except (QianchuanAPIException, requests.exceptions.RequestException) as e:
            logger.error(f"获取计划 {ad_id} 详情失败: {e}")
            return None

    def create_direct_ad_plan(self, advertiser_id: int, name: str, creative_list: List[Dict[str, Any]],
                             marketing_goal: str = "LIVE_PROM_GOODS", campaign_scene: str = "DAILY_SALE",
                             lab_ad_type: str = "NOT_LAB_AD", aweme_id: Optional[int] = None,
                             delivery_setting: Optional[Dict[str, Any]] = None,
                             audience: Optional[Dict[str, Any]] = None,
                             first_industry_id: Optional[int] = None,
                             second_industry_id: Optional[int] = None,
                             third_industry_id: Optional[int] = None,
                             ad_keywords: Optional[List[str]] = None) -> Dict[str, Any] | None:
        """
        创建直投计划（直播间直推创意）

        Args:
            advertiser_id: 广告主ID
            name: 计划名称
            creative_list: 直投创意列表，每个创意包含 image_mode: AWEME_LIVE_ROOM
            marketing_goal: 营销目标，默认 LIVE_PROM_GOODS
            campaign_scene: 投放场景，默认 DAILY_SALE
            lab_ad_type: 计划类型，默认 NOT_LAB_AD
            aweme_id: 抖音号ID
            delivery_setting: 投放设置
            audience: 定向设置
            first_industry_id: 一级行业分类ID
            second_industry_id: 二级行业分类ID
            third_industry_id: 三级行业分类ID
            ad_keywords: 创意标签列表

        Returns:
            Dict[str, Any] | None: 创建结果，包含 ad_id
        """
        logger.info(f"准备创建直投计划: {name}")

        # 构建直投计划配置
        plan_config = {
            "advertiser_id": advertiser_id,
            "name": name,
            "marketing_goal": marketing_goal,
            "campaign_scene": campaign_scene,
            "lab_ad_type": lab_ad_type,
            "creative_material_mode": "CUSTOM_CREATIVE",  # 直投计划使用自定义创意
            "creative_list": creative_list,  # 直投计划使用 creative_list 而不是 programmatic_creative_media_list
            "is_homepage_hide": 1,  # 业务铁律：隐藏首页
        }

        # 添加抖音号
        if aweme_id:
            plan_config["aweme_id"] = aweme_id

        # 添加投放设置
        if delivery_setting:
            plan_config["delivery_setting"] = delivery_setting
        else:
            # 默认投放设置
            plan_config["delivery_setting"] = {
                "budget_mode": "BUDGET_MODE_DAY",
                "budget": 100,  # 默认日预算100元
                "bid_type": "BID_TYPE_NO_BID",  # 默认不限成本
                "smart_bid_type": "SMART_BID_CUSTOM"
            }

        # 添加定向设置
        if audience:
            plan_config["audience"] = audience
        else:
            # 默认定向设置
            plan_config["audience"] = {
                "age": [1, 2, 3, 4, 5, 6],  # 全年龄段
                "gender": "NONE",  # 不限性别
                "region_type": "NONE"  # 不限地域
            }

        # 添加创意分类
        if first_industry_id is not None:
            plan_config["first_industry_id"] = first_industry_id
            logger.info(f"添加一级行业分类: {first_industry_id}")

        if second_industry_id is not None:
            plan_config["second_industry_id"] = second_industry_id
            logger.info(f"添加二级行业分类: {second_industry_id}")

        if third_industry_id is not None:
            plan_config["third_industry_id"] = third_industry_id
            logger.info(f"添加三级行业分类: {third_industry_id}")

        # 添加创意标签
        if ad_keywords and isinstance(ad_keywords, list) and len(ad_keywords) > 0:
            plan_config["ad_keywords"] = ad_keywords
            logger.info(f"添加创意标签: {len(ad_keywords)} 个标签")

        # 直播推广特有设置
        if marketing_goal == "LIVE_PROM_GOODS":
            plan_config["product_info"] = []
            plan_config["room_info"] = []

        logger.debug(f"直投计划配置: {json.dumps(plan_config, indent=2, ensure_ascii=False)}")

        # 调用通用创建方法
        return self.create_ad_plan(plan_config)

    def create_ad_plan(self, plan_config: Dict[str, Any]) -> Dict[str, Any] | None:
        advertiser_id = plan_config.get("advertiser_id")
        name = plan_config.get("name", "未命名计划")

        if not advertiser_id:
            logger.error("创建计划失败：plan_config 中缺少 advertiser_id。")
            return None

        logger.info(f"准备为广告主 {advertiser_id} 创建新计划: {name}")
        path = "v1.0/qianchuan/ad/create/"
        
        # [V39.6] 为创建计划增加重试机制
        max_retries = 2 # 针对临时错误，重试2次即可
        retry_delay = 15 # 基础延迟15秒

        for attempt in range(max_retries + 1):
            try:
                logger.debug(f"创建计划 Payload (尝试 {attempt + 1}): {json.dumps(plan_config, indent=2, ensure_ascii=False)}")
                response_data = self._request("POST", path, json_data=plan_config)
                
                # [V-CRITICAL-FIX] 增强对撞审和账户限制的识别
                full_error_message = response_data.get("message", "")

                # 检查特定错误码或关键字
                block_keywords = ["撞审", "限制计划创建", "封停"]
                is_blocked = response_data.get("code") == 40000 and any(keyword in full_error_message for keyword in block_keywords)
                
                if is_blocked:
                    logger.error(f"账户被限制！API返回明确的封停/撞审信息: {full_error_message}")
                    # 抛出特定异常，以便上层进行熔断处理
                    raise AccountBlockedException(code=40000, message=full_error_message, request_id=response_data.get("request_id"))

                data = response_data.get("data", {})
                if not data.get("ad_id"):
                     logger.error(f"创建计划失败，API未返回 ad_id。Response: {response_data}")
                     raise QianchuanAPIException(code=-3, message=f"API did not return ad_id. Full response: {response_data}", request_id=response_data.get("request_id"))

                logger.success(f"广告计划创建成功! Ad ID: {data.get('ad_id')}")
                return data

            except QianchuanAPIException as e:
                # 在捕获到通用API异常后，检查其内容，判断是否为账户封禁类型
                block_keywords = ["撞审", "限制计划创建", "封停"]
                is_blocked = any(keyword in e.message for keyword in block_keywords)

                if is_blocked:
                    logger.error(f"账户被限制！将抛出特定异常以便上层处理。错误: {e.message}")
                    # 将通用异常“升级”为特定异常并抛出
                    raise AccountBlockedException(e.code, e.message, e.request_id) from e

                # 只对“系统开小差”这类可恢复的临时错误进行重试
                if "系统开小差" in e.message and attempt < max_retries:
                    logger.warning(f"创建计划时API返回临时错误 (尝试 {attempt + 1}/{max_retries + 1}): {e.message}。将在 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
                    retry_delay *= 2 # 指数退避
                    continue
                
                # 对于其他不可恢复的错误，直接抛出
                logger.error(f"创建广告计划时发生不可重试的API错误: {e}")
                raise e
            except requests.exceptions.RequestException as e:
                 logger.error(f"创建广告计划时发生网络错误: {e}")
                 raise e

    def get_advertiser_info(self, advertiser_ids: List[int]) -> List[Dict[str, Any]] | None:
        """
        [V8.2 修复] 获取广告主信息
        """
        logger.info(f"正在获取 {len(advertiser_ids)} 个广告主的信息...")
        # [V8.2 修复] 使用正确的 v2 API 路径
        path = "2/advertiser/info/"
        params = {
            "advertiser_ids": json.dumps(advertiser_ids)
        }
        try:
            response_data = self._request("GET", path, params=params)
            data = response_data.get("data", {})
            info_list = data.get("list", [])
            logger.info(f"成功获取到 {len(info_list)} 个广告主的信息。")
            return info_list
        except (QianchuanAPIException, requests.exceptions.RequestException) as e:
            logger.error(f"获取广告主信息失败: {e}")
            return None

    def get_advertiser_list(self, cc_account_id: int, account_source: str = "QIANCHUAN", filtering: Dict[str, Any] = None, page: int = 1, page_size: int = 20) -> Dict[str, Any] | None:
        """
        获取纵横工作台下的账户列表
        """
        logger.info(f"正在获取纵横工作台 {cc_account_id} 下的广告主列表...")
        path = "2/customer_center/advertiser/list/"
        
        params = {
            "cc_account_id": cc_account_id,
            "account_source": account_source,
            "page": page,
            "page_size": page_size,
        }
        
        if filtering:
            params["filtering"] = json.dumps(filtering)
            
        try:
            response_data = self._request("GET", path, params=params)
            data = response_data.get("data", {})
            advertiser_list = data.get("list", [])
            page_info = data.get("page_info", {})
            logger.success(f"成功获取到 {len(advertiser_list)} 个广告主账户，总数: {page_info.get('total_number', 0)}。")
            return data
        except (QianchuanAPIException, requests.exceptions.RequestException) as e:
            logger.error(f"获取纵横工作台 {cc_account_id} 下的广告主列表失败: {e}")
            return None

    def get_disposal_info(self, advertiser_id: int) -> Dict[str, Any] | None:
        """
        [V6.0 新增] 调用接口查询账户的处罚信息
        """
        logger.debug(f"正在为账户 {advertiser_id} 查询处罚信息...")
        path = "v3.0/security/score_disposal_info/get/"
        params = {
            "advertiser_id": advertiser_id,
            "business_line": "QIANCHUAN",
            "filtering": json.dumps({
                "disposal_action": "ACCOUNTCLEAR",
                "disposal_status": "DISPOSAL"
            })
        }
        try:
            # 注意：此接口在 API_BASE_URL 下
            response_data = self._request("GET", path, base_url_override=API_BASE_URL, params=params)
            data = response_data.get("data", {})
            if data and data.get("disposal_info_list"):
                logger.info(f"账户 {advertiser_id} 处罚信息查询成功。")
            else:
                logger.debug(f"账户 {advertiser_id} 未查询到处罚信息。")
            return data
        except (QianchuanAPIException, requests.exceptions.RequestException) as e:
            logger.error(f"查询账户 {advertiser_id} 处罚信息失败: {e}")
            return None

    def get_score_violation_event(self, advertiser_id: int, start_time: str, end_time: str, page: int = 1, page_size: int = 10, filtering: Dict[str, Any] = None) -> List[Dict[str, Any]] | None:
        """
        [New] 查询违规积分明细
        """
        logger.debug(f"正在为账户 {advertiser_id} 查询 {start_time} 到 {end_time} 的违规积分明细...")
        path = "v3.0/security/score_violation_event/get/"
        
        params = {
            "advertiser_id": advertiser_id,
            "business_line": "QIANCHUAN",
            "start_time": start_time,
            "end_time": end_time,
            "page": page,
            "page_size": page_size,
        }
        if filtering:
            params["filtering"] = json.dumps(filtering)

        try:
            response_data = self._request("GET", path, base_url_override=API_BASE_URL, params=params)
            data = response_data.get("data", {})
            events = data.get("adv_score_event", [])
            if events:
                logger.info(f"成功获取到 {len(events)} 条违规积分明细。")
            else:
                logger.debug(f"账户 {advertiser_id} 未查询到违规积分明细。")
            return events
        except (QianchuanAPIException, requests.exceptions.RequestException) as e:
            logger.error(f"查询账户 {advertiser_id} 违规积分明细失败: {e}")
            return None

    def get_materials_in_ad(self, advertiser_id: int, ad_id: int, material_type: str = "VIDEO", audit_status: str = None) -> List[Dict[str, Any]] | None:
        """
        [V42.3] 查询指定广告计划下的素材列表及其状态，支持按审核状态过滤
        """
        logger.info(f"正在为计划 {ad_id} 查询素材状态 (过滤条件: audit_status={audit_status})...")
        path = "v1.0/qianchuan/ad/material/get/"
        
        filtering = {"material_type": material_type}
        if audit_status:
            filtering["audit_status"] = audit_status
            
        params = {
            "advertiser_id": advertiser_id,
            "ad_id": ad_id,
            "filtering": json.dumps(filtering)
        }
        try:
            response_data = self._request("GET", path, params=params)
            data = response_data.get("data", {})
            materials = data.get("ad_material_infos", [])
            logger.info(f"成功为计划 {ad_id} 查询到 {len(materials)} 个素材。")
            return materials
        except (QianchuanAPIException, requests.exceptions.RequestException) as e:
            logger.error(f"为计划 {ad_id} 查询素材状态失败: {e}")
            return None

    def authorize_aweme_account(self, advertiser_id: int, aweme_id: str, code: str) -> Dict[str, Any] | None:
        """
        [New] 授权抖音号给广告主

        Args:
            advertiser_id: 广告账户ID
            aweme_id: 要授权的抖音号
            code: 达人合作码

        Returns:
            Dict[str, Any] | None: 授权结果，包含详细的状态信息
        """
        logger.info(f"正在为广告主 {advertiser_id} 授权抖音号 {aweme_id}...")

        # 先检查账户的当前授权状态
        logger.info(f"🔍 检查广告主 {advertiser_id} 的当前授权状态...")
        try:
            auth_status = self.check_aweme_authorization_status(advertiser_id, aweme_id)
            logger.info(f"📊 授权状态检查结果: {auth_status}")

            if auth_status.get("specific_aweme_authorized"):
                logger.warning(f"⚠️ 抖音号 {aweme_id} 已经授权给广告主 {advertiser_id}")
                return {
                    "auth_success": True,
                    "error_type": "ALREADY_AUTHORIZED",
                    "message": "该抖音号已授权给此账户",
                    "note": "跳过重复授权"
                }
        except Exception as e:
            logger.warning(f"检查授权状态失败，继续执行授权: {e}")

        path = "v1.0/qianchuan/tools/aweme_auth/"

        payload = {
            "advertiser_id": advertiser_id,
            "aweme_id": aweme_id,
            "code": code,
            "auth_type": "SELF",
            "end_time": "2099-12-31 23:59:59"
        }

        # 详细记录请求信息
        logger.info(f"📤 API请求详情:")
        logger.info(f"  URL: https://api.oceanengine.com/open_api/{path}")
        logger.info(f"  Method: POST")
        logger.info(f"  Headers: Access-Token=*****, Content-Type=application/json")
        logger.info(f"  Payload: {payload}")

        try:
            # 注意：此接口在 API_BASE_URL 下
            response_data = self._request("POST", path, base_url_override=API_BASE_URL, json_data=payload)

            # 详细记录返回信息
            logger.info(f"📥 API返回详情:")
            logger.info(f"  完整响应: {response_data}")

            data = response_data.get("data", {})
            if data.get("auth_success"):
                logger.success(f"成功为广告主 {advertiser_id} 授权抖音号 {aweme_id}。")
            else:
                # V-Fix: 即使API调用本身成功(code=0)，业务上也可能失败，需要记录详细信息
                logger.error(f"为广告主 {advertiser_id} 授权抖音号 {aweme_id} 失败。API返回: {response_data}")
            return data

        except QianchuanAPIException as e:
            # 特殊处理40000错误 - PC端操作要求
            if e.code == 40000:
                logger.warning(f"广告主 {advertiser_id} 需要在千川PC端完成前置操作")
                logger.info("解决方案: 登录千川PC端后台 (https://qianchuan.jinritemai.com/) 完成首次抖音号授权")
                return {
                    "auth_success": False,
                    "error_code": "40000",
                    "error_type": "PC_OPERATION_REQUIRED",
                    "message": "需要在千川PC端完成前置操作",
                    "solution": "请登录千川PC端后台完成首次抖音号授权操作",
                    "pc_url": "https://qianchuan.jinritemai.com/",
                    "steps": [
                        "1. 登录千川PC端后台",
                        "2. 进入账户管理 -> 抖音号授权页面",
                        "3. 手动完成首次抖音号授权操作",
                        "4. 确认签署所有必要的协议"
                    ]
                }
            # 特殊处理重复授权错误
            elif "该授权类型已存在" in e.message or "不支持重复授权" in e.message:
                logger.info(f"广告主 {advertiser_id} 的抖音号 {aweme_id} 已存在授权")
                return {
                    "auth_success": True,  # 视为成功，因为目标已达成
                    "error_code": "40000",
                    "error_type": "ALREADY_AUTHORIZED",
                    "message": "该抖音号已授权给此账户",
                    "note": "重复授权被视为成功状态"
                }
            else:
                logger.error(f"为广告主 {advertiser_id} 授权抖音号 {aweme_id} 失败: {e}")
                return {
                    "auth_success": False,
                    "error_code": str(e.code),
                    "error_type": "API_ERROR",
                    "message": e.message,
                    "original_error": str(e)
                }

        except requests.exceptions.RequestException as e:
            logger.error(f"为广告主 {advertiser_id} 授权抖音号 {aweme_id} 网络请求失败: {e}")
            return {
                "auth_success": False,
                "error_code": "NETWORK_ERROR",
                "error_type": "NETWORK_ERROR",
                "message": "网络请求失败",
                "original_error": str(e)
            }

    def check_aweme_authorization_status(self, advertiser_id: int, aweme_id: str = None) -> Dict[str, Any]:
        """
        检查账户的抖音号授权状态

        Args:
            advertiser_id: 广告账户ID
            aweme_id: 可选，检查特定抖音号的授权状态

        Returns:
            Dict[str, Any]: 授权状态信息
        """
        logger.info(f"正在检查广告主 {advertiser_id} 的抖音号授权状态...")

        try:
            # 获取账户下的抖音号授权列表
            auth_list = self.get_aweme_authorization_list(advertiser_id)

            if not auth_list or not auth_list.get("authorization_infos"):
                return {
                    "has_authorization": False,
                    "authorized_count": 0,
                    "specific_aweme_authorized": False,
                    "message": "该账户暂无抖音号授权"
                }

            authorizations = auth_list["authorization_infos"]
            authorized_count = len(authorizations)

            # 检查特定抖音号的授权状态
            specific_aweme_authorized = False
            if aweme_id:
                for auth in authorizations:
                    if str(auth.get("aweme_id")) == str(aweme_id):
                        specific_aweme_authorized = True
                        break

            return {
                "has_authorization": True,
                "authorized_count": authorized_count,
                "specific_aweme_authorized": specific_aweme_authorized,
                "authorizations": authorizations[:5],  # 只返回前5个授权信息
                "message": f"该账户已授权 {authorized_count} 个抖音号"
            }

        except Exception as e:
            logger.warning(f"检查广告主 {advertiser_id} 授权状态失败: {e}")
            return {
                "has_authorization": None,
                "authorized_count": 0,
                "specific_aweme_authorized": None,
                "error": str(e),
                "message": "无法获取授权状态，可能需要PC端操作"
            }

    def get_aweme_authorization_list(self, advertiser_id: int, filtering: Dict[str, Any] = None, page: int = 1, page_size: int = 100) -> Dict[str, Any] | None:
        """
        获取千川账户下抖音号授权列表

        Args:
            advertiser_id: 广告主ID
            filtering: 过滤条件
            page: 页码，默认1
            page_size: 页面大小，默认100

        Returns:
            Dict[str, Any] | None: 授权列表信息
        """
        logger.info(f"正在获取广告主 {advertiser_id} 的抖音号授权列表...")
        path = "v1.0/qianchuan/aweme_auth_list/get/"

        params = {
            "advertiser_id": advertiser_id,
            "page": page,
            "page_size": page_size
        }

        if filtering:
            params["filtering"] = filtering

        try:
            response_data = self._request("GET", path, base_url_override=API_BASE_URL, params=params)
            data = response_data.get("data", {})

            if data.get("authorization_infos"):
                auth_count = len(data["authorization_infos"])
                logger.info(f"成功获取广告主 {advertiser_id} 的抖音号授权列表，共 {auth_count} 个授权")
            else:
                logger.info(f"广告主 {advertiser_id} 暂无抖音号授权")

            return data

        except Exception as e:
            logger.error(f"获取广告主 {advertiser_id} 抖音号授权列表失败: {e}")
            return None

    def add_comment_banned_terms(self, advertiser_id: int, aweme_id: str, terms: List[str]) -> Dict[str, Any] | None:
        """
        批量添加屏蔽词

        Args:
            advertiser_id: 广告主ID
            aweme_id: 抖音号（必需参数）
            terms: 屏蔽词列表
        """
        logger.info(f"正在为广告主 {advertiser_id} 添加 {len(terms)} 个屏蔽词...")
        logger.debug(f"使用抖音号: {aweme_id}")

        path = "v3.0/tools/comment/terms_banned/add/"

        payload = {
            "advertiser_id": advertiser_id,
            "aweme_id": aweme_id,  # 根据API文档，这是必需参数
            "terms": terms
        }

        try:
            response_data = self._request("POST", path, base_url_override=API_BASE_URL, json_data=payload)
            logger.success(f"成功为广告主 {advertiser_id} 添加屏蔽词。")
            return response_data.get("data", {})
        except (QianchuanAPIException, requests.exceptions.RequestException) as e:
            logger.error(f"为广告主 {advertiser_id} 添加屏蔽词失败: {e}")
            return None

    def get_comment_list(self, advertiser_id: int, start_time: str, end_time: str, filtering: Dict[str, Any] = None, page: int = 1, page_size: int = 10) -> Dict[str, Any] | None:
        """
        [New] 获取评论列表
        """
        logger.debug(f"正在为广告主 {advertiser_id} 获取评论列表 ({start_time} - {end_time})...")
        path = "v3.0/tools/comment/get/"
        
        params = {
            "advertiser_id": advertiser_id,
            "start_time": start_time,
            "end_time": end_time,
            "page": page,
            "page_size": page_size
        }
        if filtering:
            params["filtering"] = json.dumps(filtering)

        try:
            response_data = self._request("GET", path, base_url_override=API_BASE_URL, params=params)
            data = response_data.get("data", {})
            logger.success(f"成功获取广告主 {advertiser_id} 的评论列表。")
            return data
        except (QianchuanAPIException, requests.exceptions.RequestException) as e:
            logger.error(f"获取广告主 {advertiser_id} 评论列表失败: {e}")
            return None

    def hide_comments(self, advertiser_id: int, comment_ids: List[int]) -> Dict[str, Any] | None:
        """
        [New] 隐藏评论
        """
        logger.info(f"正在为广告主 {advertiser_id} 隐藏 {len(comment_ids)} 条评论...")
        path = "v3.0/tools/comment/hide/"
        
        payload = {
            "advertiser_id": advertiser_id,
            "comment_ids": comment_ids
        }

        try:
            response_data = self._request("POST", path, base_url_override=API_BASE_URL, json_data=payload)
            logger.success(f"成功为广告主 {advertiser_id} 隐藏评论。")
            return response_data.get("data", {})
        except (QianchuanAPIException, requests.exceptions.RequestException) as e:
            logger.error(f"为广告主 {advertiser_id} 隐藏评论失败: {e}")
            return None



    def update_campaign_status(self, advertiser_id: int, ad_ids: List[int], opt_status: str,
                             schedule_fixed_range: int = None, revive_budget: float = None,
                             budget: float = None) -> Dict[str, Any] | None:
        """
        更新计划状态 - 启用/暂停/删除/复活计划

        Args:
            advertiser_id: 广告主ID
            ad_ids: 需要更新的广告计划ID列表，最多支持10个
            opt_status: 批量更新的广告计划状态
                - DISABLE: 暂停计划
                - DELETE: 删除计划
                - ENABLE: 启用计划
                - REVIVE: 复活续投计划
            schedule_fixed_range: 固定投放时长(秒)，当opt_status为REVIVE时必填
            revive_budget: 复活预算，当opt_status为REVIVE时可选
            budget: 预算，当opt_status为REVIVE时可选

        Returns:
            Dict[str, Any] | None: API响应数据，包含成功和失败的计划ID

        Doc: https://open.oceanengine.com/labels/12/docs/1697467174900748
        """
        logger.info(f"正在更新计划状态 - 广告主: {advertiser_id}, 计划数: {len(ad_ids)}, 状态: {opt_status}")
        path = "v1.0/qianchuan/ad/status/update/"

        payload = {
            "advertiser_id": advertiser_id,
            "ad_ids": ad_ids,
            "opt_status": opt_status
        }

        # 复活计划的特殊参数
        if opt_status == "REVIVE":
            if schedule_fixed_range:
                payload["schedule_fixed_range"] = schedule_fixed_range
            if revive_budget:
                payload["revive_budget"] = revive_budget
            if budget:
                payload["budget"] = budget

        try:
            response_data = self._request("POST", path, json_data=payload)
            data = response_data.get("data", {})

            success_ids = data.get("ad_ids", [])
            errors = data.get("errors", [])

            logger.success(f"计划状态更新完成 - 成功: {len(success_ids)}, 失败: {len(errors)}")
            if errors:
                for error in errors:
                    logger.warning(f"计划 {error.get('ad_id')} 更新失败: {error.get('error_message')}")

            return data
        except (QianchuanAPIException, requests.exceptions.RequestException) as e:
            logger.error(f"更新计划状态失败: {e}")
            return None

    def update_campaign_budget(self, advertiser_id: int, ad_ids: List[int], budget: float) -> Dict[str, Any] | None:
        """
        更新计划预算

        Args:
            advertiser_id: 广告主ID
            ad_ids: 需要更新的广告计划ID列表，最多支持10个
            budget: 预算金额(元)，支持两位小数

        Returns:
            Dict[str, Any] | None: API响应数据，包含成功和失败的计划ID

        Doc: https://open.oceanengine.com/labels/12/docs/1697467207831565
        """
        logger.info(f"正在更新计划预算 - 广告主: {advertiser_id}, 计划数: {len(ad_ids)}, 预算: {budget}")
        path = "v1.0/qianchuan/ad/budget/update/"

        payload = {
            "advertiser_id": advertiser_id,
            "ad_ids": ad_ids,
            "budget": budget
        }

        try:
            response_data = self._request("POST", path, json_data=payload)
            data = response_data.get("data", {})

            success_ids = data.get("ad_ids", [])
            errors = data.get("errors", [])

            logger.success(f"计划预算更新完成 - 成功: {len(success_ids)}, 失败: {len(errors)}")
            if errors:
                for error in errors:
                    logger.warning(f"计划 {error.get('ad_id')} 预算更新失败: {error.get('error_message')}")

            return data
        except (QianchuanAPIException, requests.exceptions.RequestException) as e:
            logger.error(f"更新计划预算失败: {e}")
            return None

    def update_campaign_bid(self, advertiser_id: int, ad_ids: List[int], bid: float,
                           deep_bid_type: str = None) -> Dict[str, Any] | None:
        """
        更新计划出价

        Args:
            advertiser_id: 广告主ID
            ad_ids: 需要更新的广告计划ID列表，最多支持10个
            bid: 出价金额(元)
            deep_bid_type: 深度出价类型，可选

        Returns:
            Dict[str, Any] | None: API响应数据，包含成功和失败的计划ID

        Doc: https://open.oceanengine.com/labels/12/docs/1697467222614023
        """
        logger.info(f"正在更新计划出价 - 广告主: {advertiser_id}, 计划数: {len(ad_ids)}, 出价: {bid}")
        path = "v1.0/qianchuan/ad/bid/update/"

        payload = {
            "advertiser_id": advertiser_id,
            "ad_ids": ad_ids,
            "bid": bid
        }

        if deep_bid_type:
            payload["deep_bid_type"] = deep_bid_type

        try:
            response_data = self._request("POST", path, json_data=payload)
            data = response_data.get("data", {})

            success_ids = data.get("ad_ids", [])
            errors = data.get("errors", [])

            logger.success(f"计划出价更新完成 - 成功: {len(success_ids)}, 失败: {len(errors)}")
            if errors:
                for error in errors:
                    logger.warning(f"计划 {error.get('ad_id')} 出价更新失败: {error.get('error_message')}")

            return data
        except (QianchuanAPIException, requests.exceptions.RequestException) as e:
            logger.error(f"更新计划出价失败: {e}")
            return None

    def get_campaign_audit_suggestion(self, advertiser_id: int, ad_id: int) -> Dict[str, Any] | None:
        """
        获取计划审核建议

        Args:
            advertiser_id: 广告主ID
            ad_id: 广告计划ID

        Returns:
            Dict[str, Any] | None: 审核建议数据

        Doc: https://open.oceanengine.com/labels/12/docs/1697467603007503
        """
        logger.info(f"正在获取计划审核建议 - 广告主: {advertiser_id}, 计划: {ad_id}")
        path = "v1.0/qianchuan/ad/reject_suggest/get/"

        params = {
            "advertiser_id": advertiser_id,
            "ad_id": ad_id
        }

        try:
            response_data = self._request("GET", path, params=params)
            data = response_data.get("data", {})
            logger.success(f"成功获取计划 {ad_id} 的审核建议")
            return data
        except (QianchuanAPIException, requests.exceptions.RequestException) as e:
            logger.error(f"获取计划审核建议失败: {e}")
            return None

    def delete_campaign_materials(self, advertiser_id: int, ad_id: int, material_ids: List[int]) -> Dict[str, Any] | None:
        """
        删除广告计划下的素材

        Args:
            advertiser_id: 广告主ID
            ad_id: 广告计划ID
            material_ids: 要删除的素材ID列表

        Returns:
            Dict[str, Any] | None: 删除结果

        Doc: https://open.oceanengine.com/labels/12/docs/1803706707580163
        """
        logger.info(f"正在删除计划素材 - 广告主: {advertiser_id}, 计划: {ad_id}, 素材数: {len(material_ids)}")
        path = "v1.0/qianchuan/ad/material/delete/"

        payload = {
            "advertiser_id": advertiser_id,
            "ad_id": ad_id,
            "material_ids": material_ids
        }

        try:
            response_data = self._request("POST", path, json_data=payload)
            data = response_data.get("data", {})
            logger.success(f"成功删除计划 {ad_id} 下的 {len(material_ids)} 个素材")
            return data
        except (QianchuanAPIException, requests.exceptions.RequestException) as e:
            logger.error(f"删除计划素材失败: {e}")
            return None

    def upload_image(self, advertiser_id: int, image_file_path: str, image_signature: str = None) -> Dict[str, Any] | None:
        """
        上传图片素材

        Args:
            advertiser_id: 广告主ID
            image_file_path: 图片文件路径
            image_signature: 图片签名(MD5)，可选

        Returns:
            Dict[str, Any] | None: 上传结果，包含素材ID

        Doc: https://open.oceanengine.com/labels/12/docs/1697466652499972
        """
        if not os.path.exists(image_file_path):
            logger.error(f"图片文件不存在: {image_file_path}")
            return None

        logger.info(f"正在上传图片素材 - 广告主: {advertiser_id}, 文件: {os.path.basename(image_file_path)}")
        path = "v1.0/qianchuan/file/image/ad/"

        # 计算文件签名
        if not image_signature:
            with open(image_file_path, 'rb') as f:
                image_signature = hashlib.md5(f.read()).hexdigest()

        try:
            with open(image_file_path, 'rb') as f:
                files = {
                    'advertiser_id': (None, str(advertiser_id)),
                    'image_signature': (None, image_signature),
                    'image_file': (os.path.basename(image_file_path), f, 'image/jpeg')
                }

                response_data = self._request("POST", path, files=files, timeout=30)
                data = response_data.get("data", {})

                if data.get("image_id"):
                    logger.success(f"图片上传成功 - 素材ID: {data.get('image_id')}")
                else:
                    logger.warning("图片上传响应中未找到素材ID")

                return data
        except (QianchuanAPIException, requests.exceptions.RequestException) as e:
            logger.error(f"上传图片素材失败: {e}")
            return None

    def batch_delete_videos(self, advertiser_id: int, video_ids: List[int]) -> Dict[str, Any] | None:
        """
        批量删除视频素材

        Args:
            advertiser_id: 广告主ID
            video_ids: 要删除的视频素材ID列表

        Returns:
            Dict[str, Any] | None: 删除结果

        Doc: https://open.oceanengine.com/labels/12/docs/1763394567366656
        """
        logger.info(f"正在批量删除视频素材 - 广告主: {advertiser_id}, 素材数: {len(video_ids)}")
        path = "v1.0/qianchuan/video/delete/"

        payload = {
            "advertiser_id": advertiser_id,
            "video_ids": video_ids
        }

        try:
            response_data = self._request("POST", path, json_data=payload)
            data = response_data.get("data", {})
            logger.success(f"成功删除 {len(video_ids)} 个视频素材")
            return data
        except (QianchuanAPIException, requests.exceptions.RequestException) as e:
            logger.error(f"批量删除视频素材失败: {e}")
            return None

    def batch_delete_images(self, advertiser_id: int, image_ids: List[int]) -> Dict[str, Any] | None:
        """
        批量删除图片素材

        Args:
            advertiser_id: 广告主ID
            image_ids: 要删除的图片素材ID列表

        Returns:
            Dict[str, Any] | None: 删除结果

        Doc: https://open.oceanengine.com/labels/12/docs/1763394443752520
        """
        logger.info(f"正在批量删除图片素材 - 广告主: {advertiser_id}, 素材数: {len(image_ids)}")
        path = "v1.0/qianchuan/image/delete/"

        payload = {
            "advertiser_id": advertiser_id,
            "image_ids": image_ids
        }

        try:
            response_data = self._request("POST", path, json_data=payload)
            data = response_data.get("data", {})
            logger.success(f"成功删除 {len(image_ids)} 个图片素材")
            return data
        except (QianchuanAPIException, requests.exceptions.RequestException) as e:
            logger.error(f"批量删除图片素材失败: {e}")
            return None

    def delete_plan_material(self, advertiser_id: int, ad_id: int, material_ids: List[int]) -> Dict[str, Any] | None:
        """
        删除广告计划下素材，无需通过计划更新接口删除创意

        Args:
            advertiser_id: 广告主ID
            ad_id: 计划ID
            material_ids: 待删除素材ID列表，最大支持100个素材

        Returns:
            Dict[str, Any] | None: 删除结果

        Doc: qianchuan_api_docs/投放管理/删除广告计划下素材.md
        """
        logger.info(f"正在删除计划 {ad_id} 下的素材 - 广告主: {advertiser_id}, 素材数: {len(material_ids)}")

        if not material_ids:
            logger.warning("素材ID列表为空，跳过删除操作")
            return {"success": True, "message": "无需删除"}

        if len(material_ids) > 100:
            logger.error("素材ID数量超过100个限制")
            return None

        path = "v1.0/qianchuan/ad/material/delete/"

        payload = {
            "advertiser_id": advertiser_id,
            "ad_id": ad_id,
            "material_ids": material_ids
        }

        try:
            response_data = self._request("POST", path, json_data=payload)
            data = response_data.get("data", {})
            logger.success(f"成功删除计划 {ad_id} 下的 {len(material_ids)} 个素材")
            return data
        except (QianchuanAPIException, requests.exceptions.RequestException) as e:
            logger.error(f"删除计划素材失败: {e}")
            return None

    def get_ineffective_materials(self, advertiser_id: int, filtering: Dict[str, Any] = None,
                                page: int = 1, page_size: int = 20) -> Dict[str, Any] | None:
        """
        获取低效素材

        Args:
            advertiser_id: 广告主ID
            filtering: 过滤条件
            page: 页码
            page_size: 每页数量

        Returns:
            Dict[str, Any] | None: 低效素材列表

        Doc: https://open.oceanengine.com/labels/12/docs/1754607188087808
        """
        logger.info(f"正在获取低效素材 - 广告主: {advertiser_id}")
        path = "v1.0/qianchuan/material/low_efficiency/get/"

        params = {
            "advertiser_id": advertiser_id,
            "page": page,
            "page_size": page_size
        }

        if filtering:
            params["filtering"] = json.dumps(filtering)

        try:
            response_data = self._request("GET", path, params=params)
            data = response_data.get("data", {})
            materials = data.get("list", [])
            logger.success(f"成功获取到 {len(materials)} 个低效素材")
            return data
        except (QianchuanAPIException, requests.exceptions.RequestException) as e:
            logger.error(f"获取低效素材失败: {e}")
            return None

    def get_search_keyword_report(self, advertiser_id: int, start_date: str, end_date: str,
                                fields: List[str], filtering: Dict[str, Any] = None,
                                page: int = 1, page_size: int = 20) -> Dict[str, Any] | None:
        """
        获取搜索词/关键词数据报表

        Args:
            advertiser_id: 广告主ID
            start_date: 开始日期 YYYY-MM-DD
            end_date: 结束日期 YYYY-MM-DD
            fields: 查询字段列表
            filtering: 过滤条件
            page: 页码
            page_size: 每页数量

        Returns:
            Dict[str, Any] | None: 搜索词数据报表

        Doc: https://open.oceanengine.com/labels/12/docs/1762139117875212
        """
        logger.info(f"正在获取搜索词报表 - 广告主: {advertiser_id}, 时间: {start_date} 到 {end_date}")
        path = "v1.0/qianchuan/report/search_word/get/"

        params = {
            "advertiser_id": advertiser_id,
            "start_date": start_date,
            "end_date": end_date,
            "fields": json.dumps(fields),
            "page": page,
            "page_size": page_size
        }

        if filtering:
            params["filtering"] = json.dumps(filtering)

        try:
            response_data = self._request("GET", path, params=params)
            data = response_data.get("data", {})
            reports = data.get("list", [])
            logger.success(f"成功获取到 {len(reports)} 条搜索词数据")
            return data
        except (QianchuanAPIException, requests.exceptions.RequestException) as e:
            logger.error(f"获取搜索词报表失败: {e}")
            return None

    def get_suggested_budget(self, advertiser_id: int, ad_id: int = None,
                           audience_info: Dict[str, Any] = None) -> Dict[str, Any] | None:
        """
        获取建议预算

        Args:
            advertiser_id: 广告主ID
            ad_id: 广告计划ID，可选
            audience_info: 受众信息，可选

        Returns:
            Dict[str, Any] | None: 建议预算信息

        Doc: https://open.oceanengine.com/labels/12/docs/1770008006444036
        """
        logger.info(f"正在获取建议预算 - 广告主: {advertiser_id}")
        path = "v1.0/qianchuan/suggest/budget/"

        params = {
            "advertiser_id": advertiser_id
        }

        if ad_id:
            params["ad_id"] = ad_id
        if audience_info:
            params["audience_info"] = json.dumps(audience_info)

        try:
            response_data = self._request("GET", path, params=params)
            data = response_data.get("data", {})
            logger.success("成功获取建议预算")
            return data
        except (QianchuanAPIException, requests.exceptions.RequestException) as e:
            logger.error(f"获取建议预算失败: {e}")
            return None

    def get_suggested_bid(self, advertiser_id: int, ad_id: int = None,
                        audience_info: Dict[str, Any] = None) -> Dict[str, Any] | None:
        """
        获取非ROI目标建议出价

        Args:
            advertiser_id: 广告主ID
            ad_id: 广告计划ID，可选
            audience_info: 受众信息，可选

        Returns:
            Dict[str, Any] | None: 建议出价信息

        Doc: https://open.oceanengine.com/labels/12/docs/1761144594567183
        """
        logger.info(f"正在获取建议出价 - 广告主: {advertiser_id}")
        path = "v1.0/qianchuan/suggest_bid/get/"

        params = {
            "advertiser_id": advertiser_id
        }

        if ad_id:
            params["ad_id"] = ad_id
        if audience_info:
            params["audience_info"] = json.dumps(audience_info)

        try:
            response_data = self._request("GET", path, params=params)
            data = response_data.get("data", {})
            logger.success("成功获取建议出价")
            return data
        except (QianchuanAPIException, requests.exceptions.RequestException) as e:
            logger.error(f"获取建议出价失败: {e}")
            return None

    # ==================== 新增已验证的千川API ====================
    # 以下API已通过实际调用测试，确认能正确获取千川响应

    def get_account_balance_v2(self, advertiser_id: int) -> Dict[str, Any] | None:
        """
        获取账户余额 (增强版本)

        Args:
            advertiser_id (int): 千川广告账户ID

        Returns:
            Dict[str, Any] | None: 账户余额信息
            {
                "account_total": int,      # 账户总余额（千分之一分）
                "account_valid": int,      # 可用余额（千分之一分）
                "advertiser_id": int,      # 广告主ID
                "account_general_total": int,    # 通用余额总额（千分之一分）
                "account_general_valid": int,    # 通用余额可用（千分之一分）
                "account_general_frozen": int,   # 通用余额冻结（千分之一分）
                "account_brand_total": int,      # 品牌余额总额（千分之一分）
                "account_brand_valid": int,      # 品牌余额可用（千分之一分）
                "account_brand_frozen": int,     # 品牌余额冻结（千分之一分）
                "account_bidding_total": int,    # 竞价余额总额（千分之一分）
                "account_bidding_valid": int,    # 竞价余额可用（千分之一分）
                "account_bidding_frozen": int,   # 竞价余额冻结（千分之一分）
                "account_frozen": int            # 账户冻结余额（千分之一分）
            }

        Status: ✅ 已验证 - 能正确获取千川响应
        Test Date: 2025-07-11
        Doc: qianchuan_api_docs/资金管理/获取账户余额.md
        API: https://api.oceanengine.com/open_api/v1.0/qianchuan/account/balance/get/
        """
        logger.info(f"正在获取账户余额(增强版) - 广告主: {advertiser_id}")

        path = "v1.0/qianchuan/account/balance/get/"
        params = {"advertiser_id": advertiser_id}

        try:
            response_data = self._request("GET", path, params=params)
            data = response_data.get("data", {})

            if data:
                total_yuan = data.get("account_total", 0) / 100000
                valid_yuan = data.get("account_valid", 0) / 100000
                logger.success(f"成功获取账户余额 - 总余额: {total_yuan:.2f} 元, 可用余额: {valid_yuan:.2f} 元")

            return data
        except (QianchuanAPIException, requests.exceptions.RequestException) as e:
            logger.error(f"获取账户余额失败: {e}")
            return None

    def get_qianchuan_images(self, advertiser_id: int, filtering: dict = None,
                               page: int = 1, page_size: int = 20) -> Dict[str, Any] | None:
        """
        获取千川素材库图片

        Args:
            advertiser_id (int): 广告主ID
            filtering (dict): 图片过滤条件，可选
                - material_ids: List[int] 素材id列表
                - start_time: str 开始时间 "2023-01-01"
                - end_time: str 结束时间 "2023-12-31"
                - image_mode: str 图片类型 "CREATIVE_IMAGE_MODE_LARGE"等
            page (int): 页码，默认值1
            page_size (int): 页面大小，默认值20，取值范围1-100

        Returns:
            Dict[str, Any] | None: 图片列表
            {
                "list": [
                    {
                        "material_id": int,        # 素材ID
                        "filename": str,           # 文件名
                        "image_url": str,          # 图片URL
                        "width": int,              # 宽度
                        "height": int,             # 高度
                        "size": int,               # 文件大小
                        "create_time": str,        # 创建时间
                        "signature": str           # 文件签名
                    }
                ],
                "page_info": {
                    "total_number": int,           # 总数量
                    "total_page": int,             # 总页数
                    "page": int,                   # 当前页
                    "page_size": int               # 页大小
                }
            }

        Status: ✅ 已验证 - 能正确获取千川响应
        Test Date: 2025-07-11
        Doc: qianchuan_api_docs/素材管理/获取千川素材库图片.md
        API: https://api.oceanengine.com/open_api/v1.0/qianchuan/image/get/
        """
        logger.info(f"正在获取千川素材库图片 - 广告主: {advertiser_id}")

        path = "v1.0/qianchuan/image/get/"
        params = {
            "advertiser_id": advertiser_id,
            "page": page,
            "page_size": page_size
        }

        if filtering:
            params["filtering"] = json.dumps(filtering)

        try:
            response_data = self._request("GET", path, params=params)
            data = response_data.get("data", {})
            images = data.get("list", [])

            logger.success(f"成功获取 {len(images)} 个图片素材")
            return data
        except (QianchuanAPIException, requests.exceptions.RequestException) as e:
            logger.error(f"获取千川素材库图片失败: {e}")
            return None

    def get_qianchuan_carousels(self, advertiser_id: int, filtering: dict = None,
                                  page: int = 1, page_size: int = 20) -> Dict[str, Any] | None:
        """
        获取千川素材库图文

        Args:
            advertiser_id (int): 广告主ID
            filtering (dict): 图文过滤条件，可选
                - material_ids: List[int] 素材id列表
                - start_time: str 开始时间
                - end_time: str 结束时间
            page (int): 页码，默认值1
            page_size (int): 页面大小，默认值20

        Returns:
            Dict[str, Any] | None: 图文列表
            {
                "list": [
                    {
                        "material_id": int,        # 素材ID
                        "filename": str,           # 文件名
                        "carousel_id": str,        # 图文ID
                        "image_info": [            # 图片信息列表
                            {
                                "image_url": str,  # 图片URL
                                "width": int,      # 宽度
                                "height": int      # 高度
                            }
                        ],
                        "create_time": str,        # 创建时间
                        "signature": str           # 文件签名
                    }
                ],
                "page_info": {
                    "total_number": int,           # 总数量
                    "total_page": int,             # 总页数
                    "page": int,                   # 当前页
                    "page_size": int               # 页大小
                }
            }

        Status: ✅ 已验证 - 能正确获取千川响应
        Test Date: 2025-07-11
        Doc: qianchuan_api_docs/素材管理/获取千川素材库图文.md
        API: https://api.oceanengine.com/open_api/v1.0/qianchuan/carousel/get/
        """
        logger.info(f"正在获取千川素材库图文 - 广告主: {advertiser_id}")

        path = "v1.0/qianchuan/carousel/get/"
        params = {
            "advertiser_id": advertiser_id,
            "page": page,
            "page_size": page_size
        }

        if filtering:
            params["filtering"] = json.dumps(filtering)

        try:
            response_data = self._request("GET", path, params=params)
            data = response_data.get("data", {})
            carousels = data.get("list", [])

            logger.success(f"成功获取 {len(carousels)} 个图文素材")
            return data
        except (QianchuanAPIException, requests.exceptions.RequestException) as e:
            logger.error(f"获取千川素材库图文失败: {e}")
            return None
