#!/usr/bin/env python3
"""
AI生成文件信息
================
文件类型: 长期工具
生命周期: 永久保留
创建目的: 智能提审调度器 - 集成到工作流系统
清理条件: 项目重构时可考虑删除
"""

import os
import sys
import time
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Tuple
from collections import defaultdict
from loguru import logger

from sqlalchemy.orm import joinedload, Session
from sqlalchemy import or_, and_

from qianchuan_aw.database.database import SessionLocal
from qianchuan_aw.database.models import Campaign, AdAccount, Principal
from qianchuan_aw.services.production_appeal_service import create_production_appeal_service

class SmartAppealScheduler:
    """智能提审调度器 - 集成版本"""
    
    def __init__(self, app_settings: dict):
        self.app_settings = app_settings
        self.config = app_settings.get('appeal_scheduler', {})
        self.min_plan_age_minutes = self.config.get('min_plan_age_minutes', 60)
        self.batch_size_per_account = self.config.get('batch_size_per_account', 10)
        self.max_retry_attempts = self.config.get('max_retry_attempts', 3)
        self.account_interval_seconds = self.config.get('account_interval_seconds', 5)
        
    def get_plans_grouped_by_account(self) -> Dict[str, List[dict]]:
        """
        获取按广告户分组的待提审计划
        
        优化点1: 按广告户分组，减少浏览器启动次数
        优化点2: 只处理创建时间超过阈值的计划，避免"无审核建议"错误
        """
        logger.info("🔍 智能查找待提审计划...")
        
        try:
            db = SessionLocal()
            try:
                # 计算时间阈值
                now = datetime.now(timezone.utc)
                min_creation_time = now - timedelta(minutes=self.min_plan_age_minutes)
                
                logger.info(f"⏰ 时间策略: 只处理 {self.min_plan_age_minutes} 分钟前创建的计划")
                logger.info(f"   当前时间: {now.strftime('%Y-%m-%d %H:%M:%S')}")
                logger.info(f"   最早处理时间: {min_creation_time.strftime('%Y-%m-%d %H:%M:%S')}")
                
                # 查询待提审计划
                plans_query = db.query(Campaign).options(
                    joinedload(Campaign.account).joinedload(AdAccount.principal)
                ).filter(
                    Campaign.status == 'AUDITING',
                    # 确保未提审过
                    Campaign.appeal_status.is_(None),
                    Campaign.first_appeal_at.is_(None),
                    or_(Campaign.appeal_attempt_count.is_(None), Campaign.appeal_attempt_count == 0),
                    # 创建时间超过阈值（关键优化）
                    Campaign.created_at <= min_creation_time
                ).order_by(
                    # 按主体和账户排序，确保同一广告户聚集
                    Campaign.account.has(AdAccount.principal.has(Principal.name)),
                    Campaign.account.has(AdAccount.account_id_qc),
                    Campaign.created_at
                )
                
                plans = plans_query.all()
                
                if not plans:
                    logger.info("✅ 没有符合条件的待提审计划")
                    return {}
                
                logger.info(f"📊 找到 {len(plans)} 个符合条件的计划")
                
                # 按广告户分组（关键优化）
                grouped_plans = defaultdict(list)
                
                for plan in plans:
                    principal_name = plan.account.principal.name
                    account_id = plan.account.account_id_qc
                    account_key = f"{principal_name}_{account_id}"
                    
                    # 计算计划年龄
                    plan_age_minutes = (now - plan.created_at.replace(tzinfo=timezone.utc)).total_seconds() / 60
                    
                    plan_info = {
                        'campaign_id': plan.campaign_id_qc,
                        'principal_name': principal_name,
                        'account_id': account_id,
                        'created_at': plan.created_at,
                        'age_minutes': int(plan_age_minutes)
                    }
                    
                    grouped_plans[account_key].append(plan_info)
                
                # 统计信息
                logger.info(f"📋 智能分组结果:")
                total_plans = 0
                for account_key, account_plans in grouped_plans.items():
                    principal_name = account_plans[0]['principal_name']
                    account_id = account_plans[0]['account_id']
                    avg_age = sum(p['age_minutes'] for p in account_plans) / len(account_plans)
                    logger.info(f"   🏢 {principal_name} ({account_id}): {len(account_plans)} 个计划 (平均年龄: {avg_age:.0f}分钟)")
                    total_plans += len(account_plans)
                
                logger.info(f"📊 总计: {len(grouped_plans)} 个广告户，{total_plans} 个计划")
                
                return dict(grouped_plans)
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"❌ 智能查找待提审计划失败: {e}")
            return {}
    
    def get_failed_plans_for_smart_retry(self) -> Dict[str, List[dict]]:
        """
        获取需要智能重试的失败计划
        
        优化点: 排除明显因为"计划太新"导致的失败
        """
        logger.info("🔍 智能查找需要重试的失败计划...")
        
        try:
            db = SessionLocal()
            try:
                # 查询失败计划，但排除明显因为计划太新导致的失败
                failed_plans = db.query(Campaign).options(
                    joinedload(Campaign.account).joinedload(AdAccount.principal)
                ).filter(
                    Campaign.status == 'AUDITING',
                    Campaign.appeal_status == 'submission_failed',
                    # 排除明显是计划太新导致的失败
                    ~Campaign.appeal_error_message.like('%暂无广告审核建议%'),
                    ~Campaign.appeal_error_message.like('%查询到该内容暂无广告审核建议%'),
                    # 只重试失败次数不超过限制的
                    or_(Campaign.appeal_attempt_count.is_(None), 
                        Campaign.appeal_attempt_count < self.max_retry_attempts)
                ).order_by(
                    Campaign.account.has(AdAccount.principal.has(Principal.name)),
                    Campaign.account.has(AdAccount.account_id_qc),
                    Campaign.created_at
                ).all()
                
                if not failed_plans:
                    logger.info("✅ 没有需要重试的失败计划")
                    return {}
                
                logger.info(f"📊 找到 {len(failed_plans)} 个需要重试的失败计划")
                
                # 按广告户分组
                grouped_plans = defaultdict(list)
                
                for plan in failed_plans:
                    principal_name = plan.account.principal.name
                    account_id = plan.account.account_id_qc
                    account_key = f"{principal_name}_{account_id}"
                    
                    plan_info = {
                        'campaign_id': plan.campaign_id_qc,
                        'principal_name': principal_name,
                        'account_id': account_id,
                        'created_at': plan.created_at,
                        'error_message': plan.appeal_error_message,
                        'attempt_count': plan.appeal_attempt_count or 0
                    }
                    
                    grouped_plans[account_key].append(plan_info)
                
                # 统计信息
                logger.info(f"📋 失败计划智能分组:")
                for account_key, account_plans in grouped_plans.items():
                    principal_name = account_plans[0]['principal_name']
                    account_id = account_plans[0]['account_id']
                    logger.info(f"   🏢 {principal_name} ({account_id}): {len(account_plans)} 个失败计划")
                
                return dict(grouped_plans)
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"❌ 智能查找失败计划失败: {e}")
            return {}
    
    def execute_smart_batch_appeal(self, grouped_plans: Dict[str, List[dict]], is_retry: bool = False) -> Dict[str, int]:
        """
        执行智能批量提审
        
        优化点: 同一广告户的所有计划在一个浏览器会话中处理
        """
        operation_type = "重试" if is_retry else "提审"
        logger.info(f"🚀 开始执行智能批量{operation_type}...")
        
        if not grouped_plans:
            logger.info(f"✅ 没有需要{operation_type}的计划")
            return {}
        
        try:
            # 创建提审服务
            appeal_service = create_production_appeal_service(self.app_settings)
            
            results = {}
            total_accounts = len(grouped_plans)
            
            for i, (account_key, account_plans) in enumerate(grouped_plans.items(), 1):
                principal_name = account_plans[0]['principal_name']
                account_id = account_plans[0]['account_id']
                
                logger.info(f"\n📋 处理第 {i}/{total_accounts} 个广告户")
                logger.info(f"🏢 {principal_name} ({account_id}): {len(account_plans)} 个计划")
                logger.info("="*60)
                
                # 限制每批次的计划数量
                batch_plans = account_plans[:self.batch_size_per_account]
                if len(account_plans) > self.batch_size_per_account:
                    logger.info(f"⚠️ 计划数量超过批次限制，本次处理前 {self.batch_size_per_account} 个")
                
                # 如果是重试，需要先重置状态
                if is_retry:
                    self._reset_failed_plans_status(batch_plans)
                
                # 准备提审数据
                plans_data = []
                for plan in batch_plans:
                    plans_data.append({
                        'campaign_id': plan['campaign_id'],
                        'principal_name': plan['principal_name'],
                        'account_id': plan['account_id']
                    })
                
                try:
                    # 执行批量提审（关键优化：同一广告户在一个浏览器会话中）
                    appeal_results = appeal_service.batch_appeal_all_plans(plans_data)
                    
                    # 更新数据库
                    db = SessionLocal()
                    try:
                        updated_count = appeal_service.update_database_with_results(db, appeal_results)
                        
                        # 统计成功数量
                        success_count = sum(1 for result in appeal_results if result['success'])
                        results[account_key] = success_count
                        
                        logger.info(f"📊 广告户 {principal_name} 处理完成: {success_count}/{len(batch_plans)} 个成功")
                        
                        # 显示详细结果
                        for result in appeal_results:
                            if result['success']:
                                logger.success(f"   ✅ {result['campaign_id']}: 提审成功")
                            else:
                                logger.error(f"   ❌ {result['campaign_id']}: {result['message'][:50]}...")
                        
                    finally:
                        db.close()
                    
                except Exception as e:
                    logger.error(f"❌ 广告户 {principal_name} 处理失败: {e}")
                    results[account_key] = 0
                
                # 广告户之间等待（避免过于频繁）
                if i < total_accounts:
                    logger.info(f"⏳ 等待{self.account_interval_seconds}秒后处理下一个广告户...")
                    time.sleep(self.account_interval_seconds)
            
            # 生成总结报告
            total_success = sum(results.values())
            total_plans = sum(len(plans) for plans in grouped_plans.values())
            
            logger.info(f"\n🎉 智能批量{operation_type}完成!")
            logger.info(f"📊 总体结果: {total_success}/{total_plans} 个计划{operation_type}成功")
            logger.info(f"📊 广告户处理: {len(results)}/{total_accounts} 个广告户")
            
            return results
            
        except Exception as e:
            logger.error(f"❌ 执行智能批量{operation_type}失败: {e}")
            return {}
    
    def _reset_failed_plans_status(self, plans: List[dict]):
        """重置失败计划的状态，准备重试"""
        try:
            db = SessionLocal()
            try:
                for plan in plans:
                    campaign = db.query(Campaign).filter(
                        Campaign.campaign_id_qc == plan['campaign_id']
                    ).first()
                    
                    if campaign:
                        campaign.appeal_status = 'appeal_pending'  # 修复：设为appeal_pending而不是None，确保能被提审函数找到
                        campaign.appeal_error_message = None
                        # 保留first_appeal_at和attempt_count，用于统计
                
                db.commit()
                logger.info(f"✅ 已重置 {len(plans)} 个失败计划的状态")
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"❌ 重置失败计划状态失败: {e}")

def run_smart_appeal_scheduler(app_settings: dict) -> bool:
    """
    运行智能提审调度器
    
    Args:
        app_settings: 应用配置
        
    Returns:
        bool: 是否执行成功
    """
    logger.info("🎯 启动智能提审调度器")
    logger.info("="*80)
    logger.info("🔧 智能优化特性:")
    logger.info("1. 按广告户分组批量提审，减少浏览器启动次数")
    logger.info("2. 新建计划延迟提审，避免'无审核建议'错误")
    logger.info("3. 智能重试失败的提审，排除明显的时间问题")
    logger.info("="*80)
    
    try:
        # 创建智能调度器
        scheduler = SmartAppealScheduler(app_settings)
        
        total_success = 0
        
        # 第一阶段: 处理新建计划
        logger.info("\n🆕 第一阶段: 智能处理新建计划")
        logger.info("-" * 50)
        
        new_plans = scheduler.get_plans_grouped_by_account()
        if new_plans:
            new_results = scheduler.execute_smart_batch_appeal(new_plans, is_retry=False)
            new_success = sum(new_results.values())
            total_success += new_success
            logger.info(f"✅ 新计划处理完成: {new_success} 个成功")
        else:
            logger.info("✅ 没有符合条件的新计划")
        
        # 第二阶段: 智能重试失败计划
        logger.info("\n🔄 第二阶段: 智能重试失败计划")
        logger.info("-" * 50)
        
        failed_plans = scheduler.get_failed_plans_for_smart_retry()
        if failed_plans:
            retry_results = scheduler.execute_smart_batch_appeal(failed_plans, is_retry=True)
            retry_success = sum(retry_results.values())
            total_success += retry_success
            logger.info(f"✅ 失败重试完成: {retry_success} 个成功")
        else:
            logger.info("✅ 没有需要重试的失败计划")
        
        # 生成总结报告
        logger.success(f"\n🎉 智能提审调度完成!")
        logger.info(f"📊 总计: {total_success} 个计划提审成功")
        
        if total_success > 0:
            logger.info("\n💡 智能优化效果:")
            logger.info("- ✅ 按广告户分组，大幅减少浏览器启动次数")
            logger.info("- ✅ 延迟提审策略，避免'无审核建议'错误")
            logger.info("- ✅ 智能重试机制，提高整体成功率")
            logger.info("- ✅ 批量处理优化，提升系统效率")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 智能提审调度失败: {e}")
        return False

if __name__ == "__main__":
    # 测试运行
    import yaml
    from pathlib import Path
    
    project_root = Path(__file__).parent.parent.parent.parent
    config_path = project_root / 'config' / 'settings.yml'
    
    with open(config_path, 'r', encoding='utf-8') as f:
        app_settings = yaml.safe_load(f)
    
    success = run_smart_appeal_scheduler(app_settings)
    
    if success:
        logger.success("🎉 智能提审调度器测试成功！")
    else:
        logger.error("❌ 智能提审调度器测试失败")
